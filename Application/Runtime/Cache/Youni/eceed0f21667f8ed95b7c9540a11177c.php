<?php if (!defined('THINK_PATH')) exit();?><!DOCTYPE html>

<html>

<head>
  <meta charset="utf-8">
  <title><?php echo ($config); ?>总后台</title>
  <meta name="renderer" content="webkit">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
  <link rel="stylesheet" href="/Public/common/lib/layui/css/layui.css" media="all">
  <link rel="stylesheet" href="/Public/admin/css/admin.min.css" media="all">
  <script src="/Public/js/jquery-1.9.1.min.js"></script>
</head>
<body>
<script>
$(function(){
$('.editpic').click(function(){
	$('#file').after('<br/><input type="file" name="logo"/><br/');
})
})
</script>
  <div class="layui-fluid">
    <div class="layui-row layui-col-space15">
      <div class="layui-col-md12">
        <div class="layui-card">
          <div class="layui-card-header">分类修改</div>
          <div class="layui-card-body" pad15>
            <form action="/Youni/App/update" method="POST" enctype="multipart/form-data">
            <div class="layui-form">
			<input type="hidden" name="id" value="<?php echo ($mod["id"]); ?>">
		
              <div class="layui-form-item">
                <label class="layui-form-label">分类名称</label>
                <div class="layui-input-block">
                  <input type="text" name="title" lay-verify="require|number" autocomplete="off" class="layui-input" value="<?php echo ($mod["title"]); ?>">
                </div>
              </div>
                <div class="layui-form-item">
                <label class="layui-form-label">分类图片</label>
                <div class="layui-input-block">
                  <input type="file" id="website-title" name="picname" placeholder="图片" class="col-xs-10 " value="<?php echo ($mod["picname"]); ?>">
                </div>
                
              </div>
              </div>
             <div class="layui-form-item">
                <label class="layui-form-label">应用介绍</label>
                <div class="layui-input-block">
                  <input type="text" name="content" lay-verify="require|number" autocomplete="off" class="layui-input" value="<?php echo ($mod["content"]); ?>">
                </div>
              </div>
            
            
            <div class="layui-form-item">
                <label class="layui-form-label">应用连接</label>
                <div class="layui-input-block">
                  <input type="text" name="link" lay-verify="require|number" autocomplete="off" class="layui-input" value="<?php echo ($mod["link"]); ?>">
                </div>
              </div>
            <!--<div class="layui-form-item">-->
            <!--    <label class="layui-form-label">下载次数</label>-->
            <!--    <div class="layui-input-block">-->
            <!--      <input type="text" name="count" lay-verify="require|number" autocomplete="off" class="layui-input" value="<?php echo ($mod["count"]); ?>">-->
            <!--    </div>-->
            <!--  </div>-->
            
            
			
              <div class="layui-form-item">
                <div class="layui-input-block">
                  <button class="layui-btn" lay-submit lay-filter="card-point-set">添加</button>
                </div>
              </div>
            </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
 <script src="/Public/common/lib/layui/layui.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery-3.3.1.min.js"></script>
 <script type="text/javascript" src="/Public/common/lib/jquery/jquery.cookie.js"></script>
  
</body>
</html>