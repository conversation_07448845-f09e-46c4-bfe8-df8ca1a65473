<?php if (!defined('THINK_PATH')) exit();?><html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1">
    <link rel="icon" href="/Public/static/annie/img/fovicon.ico">
    <title><?php echo ($con["webname"]); ?>-热门视频</title>
    <meta name="google-site-verification" content="FINDSr7GU2MfCZ9NVBBLPrgGHDXfffBCbywFI5YZWOs">
    <meta name="description" content="<?php echo ($con["webname"]); ?>">
    <meta name="keywords" content="<?php echo ($con["webname"]); ?>">
    <meta name="author" content="<?php echo ($con["webname"]); ?>">
    <script defer="defer" src="/Public/static/annie/js/annie.js"></script>

    <link href="/Public/static/annie/css/annie.css" rel="stylesheet">
    <link href="/Public/static/annie/css/app.css" rel="stylesheet">
    <link href="/Public/static/annie/css/annieMod.css" rel="stylesheet">

    <style>
        .ours-select {
            position: relative
        }

        .ours-select .selection-item {
            display: block;
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px;
            position: relative
        }

        .ours-select .selection-item .arrow-svg {
            position: absolute;
            display: inline-block;
            width: 14px;
            height: 14px;
            top: 50%;
            transform: translateY(-50%);
            right: 10px
        }

        .ours-select .selection-item .arrow-svg path {
            transition: all .3s
        }

        .ours-select .selection-item .arrow-fold::before {
            right: 50%;
            top: 75%;
            transform: rotate(45deg)
        }

        .ours-select .selection-item .arrow-fold::after {
            left: 50%;
            top: 75%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::before {
            right: 50%;
            top: 25%;
            transform: rotate(-45deg)
        }

        .ours-select .selection-item .arrow-unfold::after {
            left: 50%;
            top: 25%;
            transform: rotate(45deg)
        }

        .ours-select .selector {
            top: 0;
            position: absolute;
            border-radius: 6px;
            overflow: hidden;
            width: 100%;
            background: #f7f7f7
        }

        .ours-select .selector:focus {
            outline: none
        }

        .ours-select .selector.z-index {
            z-index: 9999999999999;
            box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, .14)
        }

        .ours-select .selector .select-dropdown {
            transition: all .2s
        }

        .ours-select .selector .select-dropdown.unfold {
            max-height: 252px;
            overflow-y: auto;
            z-index: 9999999
        }

        .ours-select .selector .select-dropdown.fold {
            max-height: 0px;
            overflow-y: hidden
        }

        .ours-select .option-wrapper {
            cursor: pointer;
            width: 100%;
            height: 36px;
            line-height: 36px;
            box-sizing: border-box;
            padding: 0 32px 0 10px
        }

        .ours-select .option-wrapper:hover {
            background: #fff
        }

        .ours-select .seleted {
            background: #e6f4ff
        }

        .ours-select .option {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            user-select: none
        }
    </style>
    <style>
        .hsq-image__error,
        .hsq-image__inner,
        .hsq-image__placeholder,
        .hsq-image__wrapper {
            height: 100%;
            width: 100%
        }

        .hsq-image {
            display: inline-block;
            overflow: hidden;
            position: relative
        }

        .hsq-image__inner {
            opacity: 1;
            vertical-align: top
        }

        .hsq-image__inner.is-loading {
            opacity: 0
        }

        .hsq-image__wrapper {
            left: 0;
            position: absolute;
            top: 0
        }

        .hsq-image__error,
        .hsq-image__placeholder {
            background: var(--hsq-fill-color-light)
        }

        .hsq-image__error {
            align-items: center;
            color: var(--hsq-text-color-placeholder);
            display: flex;
            font-size: 14px;
            justify-content: center;
            vertical-align: middle
        }

        .hsq-image__preview {
            cursor: pointer
        }

        .hsq-image-viewer__wrapper {
            bottom: 0;
            left: 0;
            position: fixed;
            right: 0;
            top: 0
        }

        .hsq-image-viewer__btn {
            align-items: center;
            border-radius: 50%;
            box-sizing: border-box;
            cursor: pointer;
            display: flex;
            justify-content: center;
            opacity: .8;
            position: absolute;
            -webkit-user-select: none;
            user-select: none;
            z-index: 1
        }

        .hsq-image-viewer__btn .hsq-icon {
            cursor: pointer;
            font-size: inherit
        }

        .hsq-image-viewer__close {
            font-size: 40px;
            height: 40px;
            right: 40px;
            top: 40px;
            width: 40px
        }

        .hsq-image-viewer__canvas {
            align-items: center;
            display: flex;
            height: 100%;
            justify-content: center;
            position: static;
            -webkit-user-select: none;
            user-select: none;
            width: 100%
        }

        .hsq-image-viewer__actions {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            border-radius: 22px;
            bottom: 30px;
            height: 44px;
            left: 50%;
            padding: 0 23px;
            transform: translate(-50%);
            width: 282px
        }

        .hsq-image-viewer__actions__inner {
            align-items: center;
            color: #fff;
            cursor: default;
            display: flex;
            font-size: 23px;
            height: 100%;
            justify-content: space-around;
            width: 100%
        }

        .hsq-image-viewer__prev {
            left: 40px
        }

        .hsq-image-viewer__next,
        .hsq-image-viewer__prev {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            top: 50%;
            transform: translateY(-50%);
            width: 44px
        }

        .hsq-image-viewer__next {
            right: 40px;
            text-indent: 2px
        }

        .hsq-image-viewer__close {
            background-color: var(--hsq-text-color-regular);
            border-color: #fff;
            color: #fff;
            font-size: 24px;
            height: 44px;
            width: 44px
        }

        .hsq-image-viewer__mask {
            background: #000;
            height: 100%;
            left: 0;
            opacity: .5;
            position: absolute;
            top: 0;
            width: 100%
        }

        .viewer-fade-enter-active {
            animation: viewer-fade-in var(--hsq-transition-duration)
        }

        .viewer-fade-leave-active {
            animation: viewer-fade-out var(--hsq-transition-duration)
        }

        @keyframes viewer-fade-in {
            0% {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }

            to {
                opacity: 1;
                transform: translateZ(0)
            }
        }

        @keyframes viewer-fade-out {
            0% {
                opacity: 1;
                transform: translateZ(0)
            }

            to {
                opacity: 0;
                transform: translate3d(0, -20px, 0)
            }
        }

        .hsq-popper.is-gj-update-tips {
            background: #ff9800;
            border-radius: 4px;
            box-shadow: 0 4px 12px rgba(0, 23, 54, .1);
            color: #fff;
            padding: 20px 32px 17px
        }

        .hsq-popper.is-gj-update-tips .hsq-popper__arrow:before {
            background: #ff9800
        }

        .gj-tooltips-info[data-v-ec77157e] {
            align-items: flex-end;
            display: flex;
            flex-direction: column;
            max-width: 400px
        }

        .gj-tooltips-info .gj-info[data-v-ec77157e] {
            font-size: 15px;
            text-align: left;
            width: 100%
        }

        .gj-tooltips-info .gj-info .gj-time[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 16px;
            font-weight: 700;
            justify-content: flex-start;
            margin: 8px 0
        }

        .gj-tooltips-info .gj-info .gj-time .hsq-icon[data-v-ec77157e] {
            font-size: 20px;
            margin-right: 10px
        }

        .gj-b-g[data-v-ec77157e] {
            align-items: center;
            display: flex;
            font-size: 12px;
            justify-content: flex-end;
            margin-top: 24px;
            transform: scale(.95);
            transform-origin: right
        }

        .gj-b-g .gj-set-hide[data-v-ec77157e] {
            color: #fff;
            cursor: pointer;
            margin-right: 10px
        }

        .gj-icon-for-open[data-v-f706fe10] * {
            box-sizing: content-box
        }

        .gj-icon-for-open[data-v-f706fe10] {
            align-items: center;
            background-color: #fff5e6;
            border: 1px solid #ffcc80;
            border-bottom-right-radius: 50px;
            border-left: 0;
            border-top-right-radius: 50px;
            box-shadow: 0 0 9px 4px #ffeacc !important;
            box-sizing: content-box;
            color: #ff9800;
            cursor: pointer;
            display: flex;
            flex-direction: row;
            height: 28px;
            justify-content: center;
            padding-right: 10px;
            position: fixed;
            -webkit-user-select: none;
            z-index: 2147483647
        }

        .gj-icon-for-open>.hsq-icon[data-v-f706fe10] {
            font-size: 14px
        }

        .gj-icon[data-v-f706fe10] {
            border: 0;
            height: 18px;
            margin-left: 6px;
            padding: 0;
            pointer-events: none;
            width: 18px
        }

        .gt-text[data-v-f706fe10] {
            color: #ff9800;
            font-size: 12px;
            padding: 5px 0 5px 5px
        }

        .openGjGlobalTool[data-v-f706fe10] {
            color: #ff9800;
            padding: 5px 0
        }

        .gt-text[data-v-f706fe10],
        .openGjGlobalTool[data-v-f706fe10] {
            pointer-events: none
        }
        
        .YF28-list>.item>.right-box>video{
    width: 33vw;
}    

    </style>
    <link rel="stylesheet" type="text/css" href="css/236.b4bb815e.css">
</head>

<body inmaintabuse="1">
    <div id="app" data-v-app="">
        <div data-v-9bcfc8e6="" class="YF28">
            <div data-v-d0b9b47a="" data-v-9bcfc8e6="" class="YF28-header">
                <div data-v-d0b9b47a="" class="YF28-header-main"><img data-v-d0b9b47a=""
                        src="data:image/png;base64,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"
                        alt="" class="td-1"><img data-v-d0b9b47a=""
                        src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADEAAAAoCAYAAABXRRJPAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAZdEVYdFNvZnR3YXJlAEFkb2JlIEltYWdlUmVhZHlxyWU8AAADYUlEQVRYR9WZaU8UQRCGuRU0igkigigQRUICwagREI3igSZEEIwcghwCP1N/Gj5vUoY5ao6ePVjf5Pmy01X1znZPbfdsW606Pz/vg/swB4vwHj4aH+AVPIUJ6Id2C708yQQMgwzvwFkgP2EZdOOWtUmiYA/MwjZ45qpwAA+hsbNDgU6QeX2DnpF6cAJ3rWR9ReJB+G6FmoGWZ6+Vr00kEs/gFLxijWYBqi8xgrtAncVL3kx2oc9slRdBWv/rlqQVOIQhs1csBqt1rlpwK3EMw2YzXwx8EwlsNTQj/WbVFwPUhbzgEPZhBfRQ6td7Bp6DvpwNqLVJbEGPWY6LC1pG6tNeYBH6sZqHAUuXKcZcgUn4Al6uMry1dHFxQSa8gDx+gfZD3ZYmSMRp2/IVvNxFjFmaC/Ghvp29yKAiNiF/fZYQObQCtOxCl5lab6eluRAfdsMLKFpWn6DLwuoi8mlW9OB69bKYtfC0uHgD9HB6gZ+hw4bWVeRVY1Er9ep6aB+Xno2oGHAPonum7M4Q0fbvP+3QD2PGKFyzy7ki/3ikXhkmLTRbDOoAtUl1oEH72JWMwgLsw5nDJkxB7kxSZwk8wx7rFlYsBuc+A2buGDzzSXQzmU2BWno2Q7b84XurpDA0FzFYlkO4bSlSwpjOLp5hj2kLqyaMaM17JsugZeeeGzCm2TiKGM3jnYWFCwNdsGeGqrJs6VKSuYTZLHYtJFwYmE4Yqsp1SxkT5h4lzOZRadegm1hNmKnKE0sZE8ZuJozmccvCwkTxst2oiBVLGRPG1N7LbkdGLay8KCw8Q1XYsrQpYU6/T57pJOkNYZEo3J0wUgvfLG1KmNNGzzOdZNxCwkTxWjvTP9YsZUqYK7uXCp8JieIrCTNVWbKUMWFMByjPsEf5FwlRUXwiYaYqI5YyJoxpe+4Z9qh2tqF4B+xEzFRhAyxjXBjTizvPcBKdf6ofDzAwAqdmKJQTyNwZY6zsq9PMxlBaGKm6f5qyFClhbChhNA/3mQoWhq5C2aV1BBMW6gpjIS/wHlhYfYS5ITPpmT+Al1C4/8fYKJRZTmrBDTku62Z0PJ2EGXgMdyComMyBzhV5LxAWbXhrC6O98DpiPEq13etlCcMDsBa5gXm79H8J40L/7/2AxjwLzVL6Btra/gIPMo2eXmEMCwAAAABJRU5ErkJggg=="
                        alt="" class="td-2">
                    <div data-v-d0b9b47a="" class="text">
                        <p data-v-d0b9b47a=""><?php echo ($con["webname"]); ?>·热门视频</p>
                        <p data-v-d0b9b47a="">全网热门视频列表</p>
                    </div>
                </div>
                <div data-v-d0b9b47a="" class="YF28-nav"><a
                        data-v-d0b9b47a="" href="/">返回首页</a></div>
            </div>
            <div data-v-9bcfc8e6="" class="YF28-list">
                 <?php if(is_array($video)): foreach($video as $key=>$vo): ?><a data-v-9bcfc8e6="" href="<?php echo ($vo["picname"]); ?>" class="item"
                    title="<?php echo ($vo["title"]); ?>">
                    <div data-v-9bcfc8e6="" class="right-box">
               
                            	<video src="<?php echo ($vo["picname"]); ?>" controls loop ></video>
                       </div>
                            
                    <div data-v-9bcfc8e6="" class="left">
                        <p data-v-9bcfc8e6="" class="title"><?php echo ($vo["title"]); ?></p>
                        <p data-v-9bcfc8e6="" class="info"><?php echo ($vo["content"]); ?></p>
                      
                               <div data-v-9bcfc8e6="" class="btn">
                                 
                                <img data-v-9bcfc8e6="" src="/Public/static/annie/svg/one.02d0f60d.svg">
                                
                                <span data-v-9bcfc8e6="">点击播放</span>
                                
                                <img data-v-9bcfc8e6="" src="/Public/static/annie/svg/two.8b8129c5.svg">
                                
                                </div>
                       
                        
                    </div>
                </a><?php endforeach; endif; ?>	 
                
                </div>
        </div>
    </div>
    <script src="chrome-extension://bcjkohpafljnigdmnlaghjhaaleonlac/sm.bundle.js" data-pname="fatkun-mv3-new"
        data-asset-path="https://fkm3n.s3.ap-northeast-2.amazonaws.com"></script>
    <div id="__REC__-container"></div>
    <div id="hsq-popper-container-6889"><!--v-if--></div><span data-v-app="">
        <div data-v-f706fe10="" class="gj-icon-for-open hsq-tooltip__trigger hsq-tooltip__trigger"
            style="left: 0px; top: 84.4px; display: none;">
            <div data-v-f706fe10="" class="gj-icon gjt-flex2-x"><img data-v-adb92754="" data-v-f706fe10=""
                    src="https://s.dxcdn.cn/hsq_8_5/assets/svg/65a791c1.LVc347ie.svg" alt="哈"
                    style="width: 100%; height: 100%; margin: 0px;"></div><span data-v-f706fe10="" class="gt-text"
                style="display: none;">展开工具箱</span><i data-v-f706fe10="" class="hsq-icon" style="display: none;"><svg
                    data-v-f706fe10="" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1024 1024">
                    <path fill="currentColor"
                        d="M338.752 104.704a64 64 0 0 0 0 90.496l316.8 316.8-316.8 316.8a64 64 0 0 0 90.496 90.496l362.048-362.048a64 64 0 0 0 0-90.496L429.248 104.704a64 64 0 0 0-90.496 0z">
                    </path>
                </svg></i>
        </div><!---->
    </span>
</body><fatkun-drop-panel style="display: none;"></fatkun-drop-panel>

</html>